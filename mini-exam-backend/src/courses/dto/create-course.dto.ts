import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateCourseDto {
  @ApiProperty({ description: 'Name of the course' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: 'ID of the course group' })
  @IsInt()
  courseGroupId: number;

  @ApiProperty({ description: 'Positive coefficient (1-99)', minimum: 1, maximum: 99 })
  @IsInt()
  @Min(1)
  @Max(99)
  positiveCoefficient: number;

  @ApiProperty({ description: 'Negative coefficient (1-99)', minimum: 1, maximum: 99 })
  @IsInt()
  @Min(1)
  @Max(99)
  negativeCoefficient: number;
}
